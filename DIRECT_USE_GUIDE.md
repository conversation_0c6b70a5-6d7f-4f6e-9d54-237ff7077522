# Firecrawl 超时优化 - 直接使用指南

## 配置已直接生效

所有超时配置已经直接修改到代码中，**无需任何额外配置**，直接启动即可使用：

### 1. 直接启动（推荐）
```bash
# 使用 docker-compose（推荐）
docker-compose up -d

# 或者直接运行
npm run start:production
```

### 2. 使用 Augment MCP Server
将 `augment-mcp-config.json` 添加到您的 Augment 配置中：

```json
{
  "mcpServers": {
    "firecrawl-local": {
      "command": "npx",
      "args": ["-y", "@mendable/firecrawl-js@latest"],
      "env": {
        "FIRECRAWL_API_URL": "http://localhost:3002",
        "FIRECRAWL_API_KEY": "fc-your-api-key-here"
      }
    }
  }
}
```

## 已修改的超时配置

所有超时时间已设置为 **30 分钟（1,800,000 毫秒）**：

- ✅ API 默认超时：30 分钟
- ✅ Playwright 引擎超时：30 分钟  
- ✅ Fetch 引擎超时：30 分钟
- ✅ 所有特殊功能超时（extract、agent、proxy）：30 分钟
- ✅ 工作队列超时：30 分钟
- ✅ 测试超时：30 分钟
- ✅ SDK 默认超时：30 分钟

## 验证配置

启动后，您可以测试抓取大型网页：

```bash
curl -X POST http://localhost:3002/v1/scrape \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer fc-your-api-key" \
  -d '{
    "url": "https://example.com/large-page",
    "formats": ["markdown"]
  }'
```

## 环境变量（可选）

如果需要进一步自定义，可以设置环境变量：

```bash
export DEFAULT_TIMEOUT=3600000  # 1小时
export SCRAPE_TIMEOUT=3600000   # 1小时
# ... 其他超时配置
```

## 重要说明

- **无需修改任何配置文件**
- **无需设置环境变量**
- **直接启动即可使用 30 分钟超时**
- 如果需要更长超时，可以通过环境变量覆盖
- 所有配置都有合理的默认值

现在您可以放心地抓取任何大型网页，系统会耐心等待直到完成！
