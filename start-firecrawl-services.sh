#!/bin/bash

# Firecrawl 服务开机自启脚本
# 作者: Augment Agent
# 日期: $(date)

# 设置日志文件
LOG_FILE="$HOME/Library/Logs/firecrawl-startup.log"
DOCKER_APP_PATH="/Volumes/Mac-a/Applications/Docker.app"
PROJECT_PATH="/Volumes/Mac-a/Documents/_/Firecrawl/firecrawl"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log "=== Firecrawl 服务启动脚本开始 ==="

# 等待外部卷挂载
log "等待外部卷挂载..."
max_wait=60
wait_count=0
while [ ! -d "$DOCKER_APP_PATH" ] && [ $wait_count -lt $max_wait ]; do
    sleep 2
    wait_count=$((wait_count + 1))
    log "等待外部卷挂载... ($wait_count/$max_wait)"
done

if [ ! -d "$DOCKER_APP_PATH" ]; then
    log "错误: Docker应用路径不存在: $DOCKER_APP_PATH"
    exit 1
fi

# 检查Docker Desktop是否已经运行
if pgrep -f "Docker Desktop" > /dev/null; then
    log "Docker Desktop 已经在运行"
else
    log "启动 Docker Desktop..."
    open "$DOCKER_APP_PATH"
    
    # 等待Docker Desktop启动
    log "等待 Docker Desktop 启动..."
    max_wait=120
    wait_count=0
    while ! docker info > /dev/null 2>&1 && [ $wait_count -lt $max_wait ]; do
        sleep 3
        wait_count=$((wait_count + 1))
        log "等待 Docker 服务启动... ($wait_count/$max_wait)"
    done
    
    if ! docker info > /dev/null 2>&1; then
        log "错误: Docker 服务启动失败"
        exit 1
    fi
    
    log "Docker Desktop 启动成功"
fi

# 等待项目路径可用
log "检查项目路径..."
if [ ! -d "$PROJECT_PATH" ]; then
    log "错误: 项目路径不存在: $PROJECT_PATH"
    exit 1
fi

# 切换到项目目录并启动服务
log "切换到项目目录: $PROJECT_PATH"
cd "$PROJECT_PATH" || {
    log "错误: 无法切换到项目目录"
    exit 1
}

# 检查现有容器状态
log "检查现有容器状态..."
docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep -E "(searxng|firecrawl)" | tee -a "$LOG_FILE" || log "没有发现现有的Firecrawl容器"

# 确保在正确的目录中
log "确认当前目录: $(pwd)"
if [ ! -f "docker-compose.yaml" ]; then
    log "错误: 在当前目录找不到docker-compose.yaml文件"
    exit 1
fi

# 停止可能运行的服务（避免冲突）
log "停止现有服务..."
docker-compose down 2>/dev/null || true
sleep 3

# 启动完整的 Firecrawl 服务栈
log "启动完整的 Firecrawl 服务栈..."
if docker-compose up -d; then
    log "Firecrawl 服务启动成功"
    
    # 等待服务完全启动
    sleep 10
    
    # 等待服务完全启动
    log "等待服务完全启动..."
    sleep 15

    # 检查所有服务状态
    log "检查所有服务状态..."
    docker-compose ps | tee -a "$LOG_FILE"

    # 检查各个服务是否正常运行
    services_ok=0

    # 检查SearXNG
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080 | grep -q "200"; then
        log "✅ SearXNG 服务正常运行 (http://localhost:8080)"
        services_ok=$((services_ok + 1))
    else
        log "❌ SearXNG 服务未正常启动"
    fi

    # 检查Firecrawl API
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3002 | grep -q "200\|404"; then
        log "✅ Firecrawl API 服务正常运行 (http://localhost:3002)"
        services_ok=$((services_ok + 1))
    else
        log "❌ Firecrawl API 服务未正常启动"
    fi

    # 检查Redis
    if docker exec firecrawl-redis-1 redis-cli ping 2>/dev/null | grep -q "PONG"; then
        log "✅ Redis 服务正常运行"
        services_ok=$((services_ok + 1))
    else
        log "❌ Redis 服务未正常启动"
    fi

    # 检查Playwright服务
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep "firecrawl-playwright-service-1" | grep -q "Up"; then
        log "✅ Playwright 服务正常运行"
        services_ok=$((services_ok + 1))
    else
        log "❌ Playwright 服务未正常启动"
    fi

    # 检查Worker服务
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep "firecrawl-worker-1" | grep -q "Up"; then
        log "✅ Worker 服务正常运行"
        services_ok=$((services_ok + 1))
    else
        log "❌ Worker 服务未正常启动"
    fi

    log "服务启动完成: $services_ok/5 个服务正常运行"
    
else
    log "错误: Firecrawl 服务启动失败"
    exit 1
fi

log "=== Firecrawl 服务启动脚本完成 ==="
