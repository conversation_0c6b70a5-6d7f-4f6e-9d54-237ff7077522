name: JS SDK Test Suite

on:
  pull_request:
    branches:
      - main
    paths:
      - apps/js-sdk/firecrawl/**

env:
  TEST_API_KEY: ${{ secrets.TEST_API_KEY }}

jobs:
  test:
    name: Run tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"
          cache: "npm"
          cache-dependency-path: './apps/js-sdk/firecrawl/package-lock.json'
      - name: Install dependencies
        run: npm install
        working-directory: ./apps/js-sdk/firecrawl
      - name: Run tests
        run: npm run test
        working-directory: ./apps/js-sdk/firecrawl
