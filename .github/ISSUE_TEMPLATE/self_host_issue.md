---
name: Self-host issue
about: Report an issue with self-hosting Firecrawl
title: "[Self-Host] "
labels: self-host
assignees: ''

---

**Describe the Issue**
Provide a clear and concise description of the self-hosting issue you're experiencing.

**To Reproduce**
Steps to reproduce the issue:
1. Configure the environment or settings with '...'
2. Run the command '...'
3. Observe the error or unexpected output at '...'
4. Log output/error message

**Expected Behavior**
A clear and concise description of what you expected to happen when self-hosting.

**Screenshots**
If applicable, add screenshots or copies of the command line output to help explain the self-hosting issue.

**Environment (please complete the following information):**
- OS: [e.g. macOS, Linux, Windows]
- Firecrawl Version: [e.g. 1.2.3]
- Node.js Version: [e.g. 14.x]
- Docker Version (if applicable): [e.g. 20.10.14]
- Database Type and Version: [e.g. PostgreSQL 13.4]

**Logs**
If applicable, include detailed logs to help understand the self-hosting problem.

**Configuration**
Provide relevant parts of your configuration files (with sensitive information redacted).

**Additional Context**
Add any other context about the self-hosting issue here, such as specific infrastructure details, network setup, or any modifications made to the original Firecrawl setup.
