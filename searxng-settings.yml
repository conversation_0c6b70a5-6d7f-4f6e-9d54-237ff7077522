# SearXNG 简化配置文件
# 专注于基本功能，避免网络连接问题

# 全局设置
use_default_settings: true

general:
  debug: false
  instance_name: "Firecrawl-SearXNG"

server:
  port: 8080
  bind_address: "0.0.0.0"
  secret_key: "firecrawl-searxng-2024-secure-key-change-me"
  limiter: false
  image_proxy: false

ui:
  default_theme: simple
  theme_args:
    simple_style: auto

search:
  safe_search: 0
  autocomplete: ""
  default_lang: ""
  formats:
    - html
    - json

# DOI resolver 配置
default_doi_resolver: "oadoi.org"

# 网络配置 - 超长超时时间设置，确保大型网页不会超时
outgoing:
  request_timeout: 1800.0  # 30分钟超时
  useragent_suffix: ""
  pool_connections: 50
  pool_maxsize: 20
  enable_http2: false
  max_redirects: 20
  keepalive_expiry: 300.0  # 5分钟保持连接
  retries: 3

# Redis配置
redis:
  url: false

# 禁用可能导致问题的插件
enabled_plugins: []

# DOI resolvers
doi_resolvers:
  oadoi.org: "https://oadoi.org/"
