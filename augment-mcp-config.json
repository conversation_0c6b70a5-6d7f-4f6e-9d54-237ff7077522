{"mcpServers": {"firecrawl-local": {"command": "npx", "args": ["-y", "@mendable/firecrawl-js@latest"], "env": {"FIRECRAWL_API_URL": "http://localhost:3002", "FIRECRAWL_API_KEY": "fc-your-api-key-here", "DEFAULT_TIMEOUT": "1800000", "SCRAPE_TIMEOUT": "1800000", "PLAYWRIGHT_TIMEOUT": "1800000", "SEARCH_TIMEOUT": "1800000", "AXIOS_TIMEOUT": "1800000", "REQUEST_TIMEOUT": "1800000"}}, "firecrawl-production": {"command": "npx", "args": ["-y", "@mendable/firecrawl-js@latest"], "env": {"FIRECRAWL_API_URL": "https://api.firecrawl.dev", "FIRECRAWL_API_KEY": "fc-your-production-api-key-here", "DEFAULT_TIMEOUT": "1800000", "SCRAPE_TIMEOUT": "1800000", "PLAYWRIGHT_TIMEOUT": "1800000", "SEARCH_TIMEOUT": "1800000", "AXIOS_TIMEOUT": "1800000", "REQUEST_TIMEOUT": "1800000"}}, "firecrawl-self-hosted": {"command": "node", "args": ["/path/to/firecrawl/apps/api/dist/index.js"], "cwd": "/path/to/firecrawl", "env": {"NODE_ENV": "production", "PORT": "3002", "HOST": "0.0.0.0", "REDIS_URL": "redis://localhost:6379", "REDIS_RATE_LIMIT_URL": "redis://localhost:6379", "PLAYWRIGHT_MICROSERVICE_URL": "http://localhost:3000/scrape", "USE_DB_AUTHENTICATION": "false", "DEFAULT_TIMEOUT": "1800000", "SCRAPE_TIMEOUT": "1800000", "PLAYWRIGHT_TIMEOUT": "1800000", "SEARCH_TIMEOUT": "1800000", "AXIOS_TIMEOUT": "1800000", "REQUEST_TIMEOUT": "1800000", "WORKER_LOCK_DURATION": "1800000", "JOB_LOCK_EXTENSION_TIME": "1800000", "WORKER_STALLED_CHECK_INTERVAL": "300000", "JOB_LOCK_EXTEND_INTERVAL": "300000", "SYS_INFO_MAX_CACHE_DURATION": "300", "LOGGING_LEVEL": "INFO"}}, "firecrawl-docker": {"command": "docker", "args": ["run", "--rm", "-p", "3002:3002", "--env-file", ".env.timeout", "-e", "DEFAULT_TIMEOUT=1800000", "-e", "SCRAPE_TIMEOUT=1800000", "-e", "PLAYWRIGHT_TIMEOUT=1800000", "-e", "SEARCH_TIMEOUT=1800000", "-e", "AXIOS_TIMEOUT=1800000", "-e", "REQUEST_TIMEOUT=1800000", "-e", "WORKER_LOCK_DURATION=1800000", "-e", "JOB_LOCK_EXTENSION_TIME=1800000", "-e", "WORKER_STALLED_CHECK_INTERVAL=300000", "-e", "JOB_LOCK_EXTEND_INTERVAL=300000", "firecrawl:latest"], "cwd": "/path/to/firecrawl"}, "firecrawl-docker-compose": {"command": "docker-compose", "args": ["--env-file", ".env.timeout", "up", "-d"], "cwd": "/path/to/firecrawl", "env": {"DEFAULT_TIMEOUT": "1800000", "SCRAPE_TIMEOUT": "1800000", "PLAYWRIGHT_TIMEOUT": "1800000", "SEARCH_TIMEOUT": "1800000", "AXIOS_TIMEOUT": "1800000", "REQUEST_TIMEOUT": "1800000", "WORKER_LOCK_DURATION": "1800000", "JOB_LOCK_EXTENSION_TIME": "1800000", "WORKER_STALLED_CHECK_INTERVAL": "300000", "JOB_LOCK_EXTEND_INTERVAL": "300000"}}}}