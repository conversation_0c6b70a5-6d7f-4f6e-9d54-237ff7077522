apiVersion: apps/v1
kind: Deployment
metadata:
  name: api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api
  template:
    metadata:
      labels:
        app: api
    spec:
      terminationGracePeriodSeconds: 180
      imagePullSecrets:
        - name: docker-registry-secret
      containers:
        - name: api
          image: ghcr.io/mendableai/firecrawl:latest
          imagePullPolicy: Always
          command: [ "node" ]
          args: [ "--max-old-space-size=6144", "dist/src/index.js" ]
          ports:
            - containerPort: 3002
          env:
            - name: FLY_PROCESS_GROUP
              value: "app"
            - name: PORT
              value: "3002"
          envFrom:
            - configMapRef:
                name: firecrawl-config
            - secretRef:
                name: firecrawl-secret
          resources:
            requests:
              memory: "4G"
              cpu: "2000m"
            limits:
              memory: "6G"
              cpu: "2000m"
          livenessProbe:
            httpGet:
              path: /v0/health/liveness
              port: 3002
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /v0/health/readiness
              port: 3002
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: api
spec:
  selector:
    app: api
  ports:
    - protocol: TCP
      port: 3002
      targetPort: 3002
