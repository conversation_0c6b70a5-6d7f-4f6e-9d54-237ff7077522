# 🔥 Firecrawl - 无超时版本

本版本已移除所有超时限制，支持长时间运行的爬虫任务。

## 快速启动

### 使用 Docker Compose 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 重启服务（应用代码修改）

```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart api
docker-compose restart worker

# 停止所有服务
docker-compose down

# 重新构建并启动（如果修改了 Dockerfile）
docker-compose up -d --build
```

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f api
docker-compose logs -f worker
docker-compose logs -f redis
```

## 主要修改

### ✅ 已移除的超时限制

1. **队列系统超时**
   - `waitForJob` 函数现在支持永久等待
   - 工作器锁定时间增加到 24 小时
   - 移除了所有队列超时检查

2. **HTTP 请求超时**
   - Python SDK: 所有请求设置 `timeout=None`
   - JavaScript SDK: 移除超时配置
   - 后端 API: 移除超时限制

3. **错误处理清理**
   - 移除 408 状态码处理
   - 清理超时相关错误消息
   - 更新 OpenAPI 文档

4. **环境配置**
   - 所有超时环境变量设置为 24 小时
   - 更新 Docker 配置
   - 修改 SearXNG 超时设置

### 🔧 配置文件

主要配置文件已更新：
- `.env.timeout` - 环境变量配置
- `docker-compose.yaml` - Docker 服务配置
- `searxng-settings.yml` - 搜索引擎超时配置

## 验证修改

启动服务后，您可以：

1. 提交一个大型网站的爬虫任务
2. 观察日志，确认不再出现 408 超时错误
3. 任务可以运行数小时而不会失败

## 注意事项

- 一次性添加大量队列任务是正常行为
- 系统有并发控制机制，不会同时执行所有任务
- 长时间运行的任务现在可以安全完成
