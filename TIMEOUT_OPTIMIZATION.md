# Firecrawl 超时优化配置

## 概述

本次修改将 Firecrawl 的所有超时配置从默认值大幅增加到 30 分钟（1,800,000 毫秒），确保即使是最大型、最复杂的网页也不会因为超时而抓取失败。

## 修改的文件和配置

### 1. 核心超时配置
- `apps/api/src/lib/default-values.ts`: 默认超时从 60 秒增加到 30 分钟
- `apps/api/src/lib/timeout.ts`: axios 超时从 5 秒增加到 30 分钟

### 2. API 控制器配置
- `apps/api/src/controllers/v1/types.ts`: 
  - 默认 API 超时从 30 秒增加到 30 分钟
  - 所有特殊情况的超时处理都增加到 30 分钟

### 3. 抓取引擎配置
- `apps/api/src/scraper/scrapeURL/index.ts`: 基础超时从 2 分钟增加到 30 分钟
- `apps/api/src/scraper/scrapeURL/engines/playwright/index.ts`: Playwright 引擎超时增加到 30 分钟
- `apps/api/src/scraper/scrapeURL/engines/fetch/index.ts`: Fetch 引擎超时增加到 30 分钟

### 4. SDK 配置
- `apps/python-sdk/firecrawl/firecrawl.py`: Python SDK 默认超时增加到 30 分钟
- `apps/rust-sdk/src/scrape.rs`: Rust SDK 默认超时注释更新

### 5. 测试配置
- `apps/api/src/__tests__/snips/lib.ts`: 测试超时从 90 秒增加到 30 分钟

### 6. 外部服务配置
- `searxng-settings.yml`: SearXNG 请求超时从 2 分钟增加到 30 分钟

### 7. 环境变量配置
- `apps/api/.env.example`: 添加了完整的超时环境变量配置
- `docker-compose.yaml`: 为所有超时环境变量设置了 30 分钟的默认值
- `.env.timeout`: 新建的专用超时配置文件

## 配置详情

### 主要超时设置
- **DEFAULT_TIMEOUT**: 1,800,000ms (30分钟)
- **SCRAPE_TIMEOUT**: 1,800,000ms (30分钟)
- **PLAYWRIGHT_TIMEOUT**: 1,800,000ms (30分钟)
- **SEARCH_TIMEOUT**: 1,800,000ms (30分钟)
- **AXIOS_TIMEOUT**: 1,800,000ms (30分钟)
- **REQUEST_TIMEOUT**: 1,800,000ms (30分钟)

### 工作队列超时设置
- **WORKER_LOCK_DURATION**: 1,800,000ms (30分钟)
- **JOB_LOCK_EXTENSION_TIME**: 1,800,000ms (30分钟)
- **WORKER_STALLED_CHECK_INTERVAL**: 300,000ms (5分钟)
- **JOB_LOCK_EXTEND_INTERVAL**: 300,000ms (5分钟)

## 使用方法

### 1. 使用环境变量
```bash
# 加载超时配置
source .env.timeout

# 或者在 docker-compose 中使用
docker-compose --env-file .env.timeout up
```

### 2. 直接在代码中使用
修改后的代码会自动使用新的超时配置，无需额外设置。

### 3. 自定义超时时间
如果需要更长的超时时间，可以：
- 修改 `.env.timeout` 文件中的值
- 设置环境变量覆盖默认值
- 在 API 调用时指定 `timeout` 参数

## 注意事项

1. **资源消耗**: 更长的超时时间意味着进程可能会占用资源更长时间
2. **并发限制**: 考虑调整并发数量以避免资源耗尽
3. **监控**: 建议监控长时间运行的抓取任务
4. **网络稳定性**: 确保网络连接稳定，避免长时间等待后仍然失败

## 验证配置

启动服务后，可以通过以下方式验证配置是否生效：
1. 查看日志中的超时设置
2. 测试抓取大型网页
3. 监控任务执行时间

## 回滚方法

如果需要回滚到原始配置，可以：
1. 恢复各文件中的原始超时值
2. 删除或重命名 `.env.timeout` 文件
3. 重启服务

这些修改确保了 Firecrawl 能够处理任何大小的网页，永远不会因为超时而失败。
