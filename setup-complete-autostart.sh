#!/bin/bash

# Firecrawl 完整开机自启设置脚本
# 提供多种开机自启解决方案

echo "🚀 Firecrawl 开机自启设置向导"
echo "================================"
echo ""

# 获取当前路径
CURRENT_DIR="/Volumes/Mac-a/Documents/_/Firecrawl/firecrawl"
STARTUP_SCRIPT="$CURRENT_DIR/start-firecrawl-services.sh"
USER=$(whoami)

echo "当前用户: $USER"
echo "项目路径: $CURRENT_DIR"
echo ""

# 检查必要文件
if [ ! -f "$STARTUP_SCRIPT" ]; then
    echo "❌ 错误: 启动脚本不存在: $STARTUP_SCRIPT"
    exit 1
fi

echo "✅ 启动脚本已找到"
chmod +x "$STARTUP_SCRIPT"

# 创建日志目录
mkdir -p "$HOME/Library/Logs"
echo "✅ 日志目录已创建"

echo ""
echo "请选择开机自启方式:"
echo "1) LaunchAgent (推荐) - 系统级自启动"
echo "2) 登录项 - 用户登录后启动"
echo "3) 手动设置 Docker Desktop 自启动"
echo "4) 全部设置"
echo ""
read -p "请输入选择 (1-4): " choice

case $choice in
    1|4)
        echo ""
        echo "🔧 设置 LaunchAgent..."
        
        PLIST_PATH="$HOME/Library/LaunchAgents/com.firecrawl.startup.plist"
        
        # 卸载现有的
        launchctl unload "$PLIST_PATH" 2>/dev/null || true
        
        # 创建新的LaunchAgent
        cat > "$PLIST_PATH" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.firecrawl.startup</string>
    
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>$STARTUP_SCRIPT</string>
    </array>
    
    <key>RunAtLoad</key>
    <true/>
    
    <key>KeepAlive</key>
    <false/>
    
    <key>StandardOutPath</key>
    <string>$HOME/Library/Logs/firecrawl-launchd.out</string>
    
    <key>StandardErrorPath</key>
    <string>$HOME/Library/Logs/firecrawl-launchd.err</string>
    
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin</string>
        <key>HOME</key>
        <string>$HOME</string>
    </dict>
    
    <key>StartInterval</key>
    <integer>60</integer>
</dict>
</plist>
EOF
        
        # 加载LaunchAgent
        if launchctl load "$PLIST_PATH" 2>/dev/null; then
            echo "✅ LaunchAgent 设置成功"
        else
            echo "⚠️  LaunchAgent 可能需要重启后生效"
        fi
        ;;
esac

case $choice in
    2|4)
        echo ""
        echo "🔧 设置登录项..."
        
        # 使用AppleScript添加登录项
        APP_PATH="$CURRENT_DIR/Firecrawl-Startup.app"
        
        osascript << EOF
tell application "System Events"
    try
        delete login item "Firecrawl Startup"
    end try
    make login item at end with properties {path:"$APP_PATH", hidden:true}
end tell
EOF
        
        if [ $? -eq 0 ]; then
            echo "✅ 登录项设置成功"
        else
            echo "⚠️  登录项设置可能需要手动完成"
            echo "   请在 系统偏好设置 > 用户与群组 > 登录项 中添加:"
            echo "   $APP_PATH"
        fi
        ;;
esac

case $choice in
    3|4)
        echo ""
        echo "🔧 设置 Docker Desktop 自启动..."
        
        # 检查Docker Desktop是否在登录项中
        if osascript -e 'tell application "System Events" to get the name of every login item' | grep -q "Docker"; then
            echo "✅ Docker Desktop 已在登录项中"
        else
            echo "添加 Docker Desktop 到登录项..."
            osascript << EOF
tell application "System Events"
    make login item at end with properties {path:"/Volumes/Mac-a/Applications/Docker.app", hidden:false}
end tell
EOF
            if [ $? -eq 0 ]; then
                echo "✅ Docker Desktop 登录项设置成功"
            else
                echo "⚠️  请手动添加 Docker Desktop 到登录项"
                echo "   路径: /Volumes/Mac-a/Applications/Docker.app"
            fi
        fi
        ;;
esac

echo ""
echo "🎉 设置完成!"
echo ""
echo "📋 重要信息:"
echo "- 启动脚本: $STARTUP_SCRIPT"
echo "- 日志位置: $HOME/Library/Logs/firecrawl-startup.log"
echo "- LaunchAgent日志: $HOME/Library/Logs/firecrawl-launchd.out"
echo ""
echo "🧪 测试命令:"
echo "bash '$STARTUP_SCRIPT'"
echo ""
echo "📊 查看状态:"
echo "launchctl list | grep firecrawl"
echo ""
echo "🗑️  卸载自启动:"
echo "launchctl unload '$HOME/Library/LaunchAgents/com.firecrawl.startup.plist'"
echo ""
echo "⚠️  注意: 重启系统后生效。如果遇到问题，请查看日志文件。"
