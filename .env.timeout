# Firecrawl 永不超时配置文件
# 专为长时间运行的爬虫任务优化，移除所有超时限制

# 基础超时配置 - 设置为极长时间（24小时）
DEFAULT_TIMEOUT=86400000
SCRAPE_TIMEOUT=86400000
PLAYWRIGHT_TIMEOUT=86400000
SEARCH_TIMEOUT=86400000
AXIOS_TIMEOUT=86400000
REQUEST_TIMEOUT=86400000

# 工作队列超时配置 - 24小时锁定，1小时检查间隔
WORKER_LOCK_DURATION=86400000
JOB_LOCK_EXTENSION_TIME=86400000
WORKER_STALLED_CHECK_INTERVAL=3600000
JOB_LOCK_EXTEND_INTERVAL=60000

# 系统监控超时配置
SYS_INFO_MAX_CACHE_DURATION=300

# 说明：
# - 所有超时时间设置为86400000毫秒（24小时）
# - 这确保爬虫可以运行数小时而不会因为超时而失败
# - 工作队列锁定时间设置为24小时，防止长时间运行的任务被标记为停滞
# - 如果您需要更长的超时时间，可以将这些值进一步增加
# - 这些设置专为需要长时间运行的爬虫任务优化
