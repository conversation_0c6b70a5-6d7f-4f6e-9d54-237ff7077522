# Firecrawl 超时配置文件
# 专为大型网页抓取优化，所有超时时间设置为30分钟

# 基础超时配置
DEFAULT_TIMEOUT=1800000
SCRAPE_TIMEOUT=1800000
PLAYWRIGHT_TIMEOUT=1800000
SEARCH_TIMEOUT=1800000
AXIOS_TIMEOUT=1800000
REQUEST_TIMEOUT=1800000

# 工作队列超时配置
WORKER_LOCK_DURATION=1800000
JOB_LOCK_EXTENSION_TIME=1800000
WORKER_STALLED_CHECK_INTERVAL=300000
JOB_LOCK_EXTEND_INTERVAL=300000

# 系统监控超时配置
SYS_INFO_MAX_CACHE_DURATION=300

# 说明：
# - 所有超时时间设置为1800000毫秒（30分钟）
# - 这确保即使是最大型、最复杂的网页也不会因为超时而失败
# - 如果您需要更长的超时时间，可以将这些值进一步增加
# - 建议在生产环境中根据实际需求调整这些值
