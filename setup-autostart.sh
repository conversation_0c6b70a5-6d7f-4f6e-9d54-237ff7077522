#!/bin/bash

# Firecrawl 开机自启设置脚本
# 这个脚本会设置开机自启

echo "=== Firecrawl 开机自启设置 ==="

# 获取当前用户
USER=$(whoami)
echo "当前用户: $USER"

# 设置路径
SCRIPT_PATH="/Volumes/Mac-a/Documents/_/Firecrawl/firecrawl/start-firecrawl-services.sh"
PLIST_PATH="$HOME/Library/LaunchAgents/com.firecrawl.startup.plist"

echo "脚本路径: $SCRIPT_PATH"
echo "LaunchAgent路径: $PLIST_PATH"

# 检查脚本是否存在
if [ ! -f "$SCRIPT_PATH" ]; then
    echo "错误: 启动脚本不存在: $SCRIPT_PATH"
    exit 1
fi

# 确保脚本有执行权限
chmod +x "$SCRIPT_PATH"
echo "✅ 设置脚本执行权限"

# 创建日志目录
mkdir -p "$HOME/Library/Logs"
echo "✅ 创建日志目录"

# 卸载现有的LaunchAgent（如果存在）
if launchctl list | grep -q "com.firecrawl.startup"; then
    echo "卸载现有的LaunchAgent..."
    launchctl unload "$PLIST_PATH" 2>/dev/null || true
fi

# 创建新的LaunchAgent配置
cat > "$PLIST_PATH" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.firecrawl.startup</string>
    
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>$SCRIPT_PATH</string>
    </array>
    
    <key>RunAtLoad</key>
    <true/>
    
    <key>KeepAlive</key>
    <false/>
    
    <key>StandardOutPath</key>
    <string>$HOME/Library/Logs/firecrawl-launchd.out</string>
    
    <key>StandardErrorPath</key>
    <string>$HOME/Library/Logs/firecrawl-launchd.err</string>
    
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin</string>
        <key>HOME</key>
        <string>$HOME</string>
    </dict>
</dict>
</plist>
EOF

echo "✅ 创建LaunchAgent配置文件"

# 加载LaunchAgent
if launchctl load "$PLIST_PATH"; then
    echo "✅ LaunchAgent加载成功"
else
    echo "❌ LaunchAgent加载失败，尝试使用bootstrap..."
    launchctl bootstrap gui/$(id -u) "$PLIST_PATH" 2>/dev/null || echo "Bootstrap也失败了"
fi

# 验证LaunchAgent是否加载
if launchctl list | grep -q "com.firecrawl.startup"; then
    echo "✅ LaunchAgent已成功加载"
else
    echo "⚠️  LaunchAgent可能未正确加载，但配置文件已创建"
fi

echo ""
echo "=== 设置完成 ==="
echo "开机自启已配置。重启系统后，Firecrawl服务将自动启动。"
echo ""
echo "日志文件位置:"
echo "- 启动脚本日志: $HOME/Library/Logs/firecrawl-startup.log"
echo "- LaunchAgent输出: $HOME/Library/Logs/firecrawl-launchd.out"
echo "- LaunchAgent错误: $HOME/Library/Logs/firecrawl-launchd.err"
echo ""
echo "手动测试启动脚本:"
echo "bash '$SCRIPT_PATH'"
echo ""
echo "查看LaunchAgent状态:"
echo "launchctl list | grep firecrawl"
echo ""
echo "卸载开机自启:"
echo "launchctl unload '$PLIST_PATH'"
